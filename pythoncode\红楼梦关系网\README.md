# 红楼梦人物关系网络分析系统

这是一个基于NetworkX和影响力最大化理论的红楼梦人物关系网络分析系统，包含网络构建分析和黛玉之死预测两大功能模块。

## 🎯 项目概述

本项目将文学分析与网络科学相结合，通过构建红楼梦人物关系网络，深入分析人物间的复杂关系，并基于影响力最大化理论预测林黛玉的悲剧命运。

## 📋 功能模块

### 1. 🌐 红楼梦人物关系网络构建与分析
- 从CSV文件直接构建人物关系网络
- 计算多种网络统计特征（度分布、中心性指标、聚类系数等）
- 生成多种中心性指标排名（度中心性、介数中心性、接近中心性、特征向量中心性）
- 智能化网络可视化：
  - 层次化布局展现人物关系结构
  - 根据重要性用不同颜色标识人物
  - 核心人物关系子网络图
  - 度分布统计图表
- 生成详细的分析报告
- 导出标准网络数据格式（边列表txt文件）

### 2. 🎯 基于影响力最大化的黛玉之死预测
- 独立级联模型(IC)模拟负面影响传播
- 反向影响路径分析找出关键影响者
- 影响阻断分析识别潜在拯救者
- 关键路径分析揭示传播机制
- 脆弱性评估量化风险等级
- 网络可视化展示影响关系

## 🛠️ 环境要求

- Python 3.7+
- 必要的Python包（见requirements.txt）

## 📦 安装步骤

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 确保数据文件在当前目录下：
   - `triples.csv`: 人物关系三元组数据
   - `honglou.txt`: 红楼梦原文文本

## 🚀 使用方法

### 方式一：使用主程序（推荐）
```bash
python main.py
```

主程序提供交互式菜单，可以选择：
1. 🌐 红楼梦人物关系网络构建与分析
2. 🎯 基于影响力最大化的黛玉之死预测
3. 🔄 运行完整分析（先网络分析，后预测分析）

### 方式二：单独运行模块
```bash
# 仅运行网络分析
python honglou_network.py

# 仅运行黛玉预测（需要先运行网络分析生成数据文件）
python 黛玉之死预测.py
```

## 📁 输出文件结构

程序采用模块化输出，结果分别保存在不同目录：

### 📊 网络分析结果 (`network_analysis/`)
- `network_stats.json` - 完整的网络统计数据
- `basic_stats.csv` - 基本网络统计
- `degree_centrality.csv` - 度中心性排名
- `betweenness_centrality.csv` - 介数中心性排名
- `closeness_centrality.csv` - 接近中心性排名
- `eigenvector_centrality.csv` - 特征向量中心性排名
- `degree_distribution.csv` - 度分布数据
- `network_graph.png` - 主要连通分量网络关系图
- `core_network.png` - 核心人物关系子网络图
- `degree_distribution.png` - 度分布图
- `analysis_report.txt` - 网络分析报告摘要

### 🎯 黛玉预测结果 (`daiyu_prediction/`)
- `黛玉之死影响力分析报告.txt` - 详细的预测分析报告
- `影响网络图.png` - 影响网络可视化图

### 🔗 网络数据文件（当前目录）
- `network_data.txt` - 网络边列表格式数据
- `node_mapping.txt` - 节点编号与人物姓名对应表

## 网络统计指标说明

- **度中心性**: 衡量节点的直接连接数量
- **介数中心性**: 衡量节点在网络中的桥梁作用
- **接近中心性**: 衡量节点到其他节点的平均距离
- **特征向量中心性**: 考虑邻居节点重要性的中心性指标
- **聚类系数**: 衡量节点邻居之间的连接密度
- **网络密度**: 实际边数与可能边数的比例
- **平均路径长度**: 网络中任意两点间最短路径的平均长度

## 可视化特色

### 网络布局优化
- **层次化布局**：核心人物位于中心，其他人物按与核心人物的距离分层排列
- **颜色编码**：
  - 红色：核心人物（度数最高的5个）
  - 橙色：重要人物（度数≥5）
  - 黄色：次要人物（度数≥3）
  - 浅蓝色：普通人物
- **节点大小**：根据人物的度数（关系数量）动态调整
- **智能标签**：只为重要人物显示姓名标签，避免图像过于拥挤

### 多层次分析
1. **主网络图**：展示最大连通分量的整体结构
2. **核心网络图**：聚焦核心人物及其直接关系
3. **统计图表**：度分布等数据可视化

## 网络数据格式说明

### network_data.txt 文件格式
```
388 370          # 第一行：节点数 边数
0 1              # 后续行：边的两个端点编号
0 3
0 4
...
```

### node_mapping.txt 文件格式
```
节点编号    人物姓名
0          贾代善
1          贾源
2          娄氏
...
```

这种格式便于导入其他网络分析工具（如Gephi、Cytoscape等）进行进一步分析。

## 🔬 分析方法说明

### 网络分析方法
- **度中心性**: 衡量节点的直接连接数量
- **介数中心性**: 衡量节点在网络中的桥梁作用
- **接近中心性**: 衡量节点到其他节点的平均距离
- **特征向量中心性**: 考虑邻居节点重要性的中心性指标
- **聚类系数**: 衡量节点邻居之间的连接密度

### 影响力预测方法
- **独立级联模型(IC)**: 模拟负面影响在网络中的传播过程
- **反向影响分析**: 追踪影响目标节点的关键路径
- **影响阻断分析**: 评估潜在拯救者的阻断效果
- **脆弱性评估**: 量化目标节点的网络位置风险

## ⚠️ 注意事项

1. CSV文件格式应为：source,target,relation,label
2. 大型网络的可视化可能需要较长时间
3. 程序会自动处理中文字符显示
4. 黛玉预测分析需要先运行网络分析生成数据文件
5. 多次运行会覆盖之前的结果文件

## 🔧 故障排除

- 如果出现中文显示问题，请确保系统安装了SimHei字体
- 如果内存不足，可以在代码中调整可视化参数
- 确保CSV文件编码为UTF-8格式
- 如果缺少数据文件，程序会提示并引导用户先运行相应模块

## 📚 项目特色

本项目的创新之处在于：
1. **文学与科学结合**: 将传统文学分析与现代网络科学相结合
2. **预测性分析**: 基于影响力最大化理论预测文学人物命运
3. **模块化设计**: 网络分析和预测分析相互独立，便于扩展
4. **可视化丰富**: 提供多层次、多角度的网络可视化
5. **结果可重现**: 固定文件名确保结果的一致性和可比较性
