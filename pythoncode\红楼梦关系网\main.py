#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红楼梦人物关系网络分析主程序
包含网络构建分析和黛玉之死预测两个功能模块
"""

import os
import sys
from datetime import datetime

def print_banner():
    """打印程序横幅"""
    print("=" * 80)
    print("🎭 红楼梦人物关系网络分析系统")
    print("=" * 80)
    print("📚 基于《红楼梦》人物关系的网络科学分析")
    print("🔬 包含网络构建分析和影响力预测两大功能")
    print("=" * 80)

def print_menu():
    """打印主菜单"""
    print("\n📋 请选择要执行的分析：")
    print("1. 🌐 红楼梦人物关系网络构建与分析")
    print("   - 构建人物关系网络")
    print("   - 计算网络统计特征")
    print("   - 生成可视化图表")
    print("   - 输出分析报告")
    print()
    print("2. 🎯 基于影响力最大化的黛玉之死预测")
    print("   - 独立级联模型模拟")
    print("   - 影响路径分析")
    print("   - 拯救者效果评估")
    print("   - 脆弱性评估")
    print()
    print("3. 🔄 运行完整分析（先网络分析，后预测分析）")
    print()
    print("0. 🚪 退出程序")
    print("-" * 60)

def check_dependencies():
    """检查必要的数据文件"""
    required_files = ['triples.csv', 'honglou.txt']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要的数据文件：")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保以下文件存在于当前目录：")
        print("   - triples.csv: 人物关系三元组数据")
        print("   - honglou.txt: 红楼梦原文文本")
        return False
    
    return True

def run_network_analysis():
    """运行网络分析"""
    print("\n🌐 开始红楼梦人物关系网络分析...")
    print("-" * 60)
    
    try:
        from honglou_network import HonglouNetworkAnalyzer
        
        # 创建分析器并运行分析
        analyzer = HonglouNetworkAnalyzer()
        success = analyzer.run_analysis()
        
        if success:
            print("\n✅ 网络分析完成！")
            print("📁 结果已保存到 network_analysis/ 目录")
            print("📊 主要输出文件：")
            print("   - network_stats.json: 网络统计数据")
            print("   - analysis_report.txt: 分析报告")
            print("   - network_graph.png: 网络关系图")
            print("   - 各种中心性排名CSV文件")
            return True
        else:
            print("\n❌ 网络分析失败，请检查数据文件")
            return False
            
    except ImportError as e:
        print(f"❌ 导入网络分析模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 网络分析过程中出现错误: {e}")
        return False

def run_daiyu_prediction():
    """运行黛玉预测分析"""
    print("\n🎯 开始黛玉之死影响力预测分析...")
    print("-" * 60)
    
    # 检查网络数据文件是否存在
    network_files = ['network_data.txt', 'node_mapping.txt']
    missing_files = [f for f in network_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ 缺少网络数据文件，需要先运行网络分析：")
        for file in missing_files:
            print(f"   - {file}")
        
        choice = input("\n是否先运行网络分析？(y/n): ").strip().lower()
        if choice == 'y':
            if not run_network_analysis():
                return False
        else:
            return False
    
    try:
        # 动态导入避免循环依赖
        import importlib.util
        spec = importlib.util.spec_from_file_location("daiyu_prediction", "黛玉之死预测.py")
        daiyu_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(daiyu_module)
        
        # 运行预测分析
        daiyu_module.main()
        
        print("\n✅ 黛玉预测分析完成！")
        print("📁 结果已保存到 daiyu_prediction/ 目录")
        print("📊 主要输出文件：")
        print("   - 黛玉之死影响力分析报告.txt: 详细分析报告")
        print("   - 影响网络图.png: 影响网络可视化")
        return True
        
    except Exception as e:
        print(f"❌ 黛玉预测分析过程中出现错误: {e}")
        return False

def run_complete_analysis():
    """运行完整分析"""
    print("\n🔄 开始完整分析流程...")
    print("=" * 60)
    
    # 先运行网络分析
    if not run_network_analysis():
        return False
    
    print("\n" + "=" * 60)
    
    # 再运行预测分析
    if not run_daiyu_prediction():
        return False
    
    print("\n🎉 完整分析流程执行完毕！")
    print("📁 所有结果已分别保存到对应目录：")
    print("   - network_analysis/: 网络分析结果")
    print("   - daiyu_prediction/: 预测分析结果")
    
    return True

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖文件
    if not check_dependencies():
        return
    
    while True:
        print_menu()
        
        try:
            choice = input("请输入选项 (0-3): ").strip()
            
            if choice == '0':
                print("\n👋 感谢使用红楼梦人物关系网络分析系统！")
                break
            elif choice == '1':
                run_network_analysis()
            elif choice == '2':
                run_daiyu_prediction()
            elif choice == '3':
                run_complete_analysis()
            else:
                print("❌ 无效选项，请输入 0-3 之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序执行出错: {e}")
            
        # 等待用户确认继续
        if choice in ['1', '2', '3']:
            input("\n按回车键继续...")

if __name__ == "__main__":
    main()
