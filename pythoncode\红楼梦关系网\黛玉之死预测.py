import networkx as nx
import numpy as np
import random
from collections import defaultdict, deque
import datetime
import os
import json
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Set
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class InfluenceMaximizationAnalyzer:
    """影响力最大化分析器：基于红楼梦人物关系网络预测黛玉之死"""

    def __init__(self):
        self.G = None
        self.node_to_name = {}
        self.name_to_node = {}
        self.daiyu_id = 127  # 林黛玉的节点编号

        # 关键人物分类（基于文学分析和网络连接情况）
        self.negative_sources = {  # 负面影响源
            106: "王夫人",     # 主要反对者
            244: "薛宝钗",     # 情敌
            137: "王熙凤",     # 权力斗争参与者
            245: "薛蟠",       # 薛家势力
            227: "薛姨妈",     # 薛家长辈
            31: "邢夫人",      # 贾赦之妻
            11: "赵姨娘",      # 贾政妾室
            # 添加更多可能的负面影响源
            0: "贾代善",       # 贾家祖先
            7: "贾赦",         # 贾家长房
            12: "贾政"         # 贾家二房
        }

        self.potential_saviors = {  # 潜在拯救者
            20: "贾宝玉",      # 恋人
            3: "贾母",         # 疼爱黛玉的长辈
            129: "紫鹊",       # 贴身丫鬟（可能是紫鹃的别名）
            5: "贾敏",         # 黛玉母亲（已故但影响仍在）
            55: "林如海",      # 黛玉父亲
            126: "雪雁"        # 黛玉的丫鬟
        }

        self.neutral_influencers = {  # 中性影响者
            16: "贾琏",        # 族中重要人物
            14: "贾珠",        # 贾政长子
            10: "贾迎春",      # 贾赦之女
            25: "贾巧姐"       # 贾琏之女
        }

    def read_node_mapping(self, mapping_file: str) -> Dict[int, str]:
        """读取节点编号到人物姓名的映射"""
        node_to_name = {}
        with open(mapping_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[1:]:  # 跳过标题行
                if line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        node_id = int(parts[0])
                        name = parts[1]
                        node_to_name[node_id] = name

        # 创建反向映射
        name_to_node = {name: node_id for node_id, name in node_to_name.items()}
        return node_to_name, name_to_node

    def read_network(self, file_path: str) -> nx.DiGraph:
        """读取关系网络文件并构建有向图"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            n, m = map(int, lines[0].strip().split())
            edges = [tuple(map(int, line.strip().split())) for line in lines[1:]]

        G = nx.DiGraph()
        G.add_nodes_from(range(n))

        # 根据人物关系类型设置不同的传播概率
        for u, v in edges:
            prob = self._calculate_edge_probability(u, v)
            G.add_edge(u, v, prob=prob, weight=prob)

        return G

    def _calculate_edge_probability(self, u: int, v: int) -> float:
        """根据人物关系计算边的传播概率"""
        # 基础概率设置更高，确保有足够的传播
        base_prob = 0.5

        # 如果是负面影响源，显著增加传播概率
        if u in self.negative_sources:
            base_prob = 0.7

        # 如果目标是黛玉，且源是负面影响者，进一步增加概率
        if v == self.daiyu_id and u in self.negative_sources:
            base_prob = 0.8

        # 如果源是黛玉的直接连接者（父亲、丫鬟等），增加概率
        if u in [55, 126]:  # 林如海、雪雁
            base_prob = 0.6

        # 如果是潜在拯救者，设置中等传播概率（可能是正面影响）
        if u in self.potential_saviors:
            base_prob = 0.4

        # 对于到达黛玉的路径，增加传播概率
        if v == self.daiyu_id:
            base_prob = min(base_prob + 0.2, 0.9)

        # 确保概率在合理范围内
        return min(max(base_prob, 0.2), 0.9)

    def simulate_IC_model(self, seeds: List[int], max_steps: int = 10) -> Tuple[Set[int], List[Tuple[int, int]]]:
        """独立级联模型：模拟负面影响传播"""
        # 过滤掉不在图中的种子节点
        valid_seeds = [s for s in seeds if s in self.G.nodes()]
        if not valid_seeds:
            return set(), [], []

        activated = set(valid_seeds)
        newly_activated = set(valid_seeds)
        propagation_path = []
        step_info = []

        for step in range(max_steps):
            next_activated = set()
            step_activations = []

            for u in newly_activated:
                if u in self.G.nodes():  # 确保节点存在
                    for v in self.G.successors(u):
                        if v not in activated and v in self.G.nodes():
                            prob = self.G[u][v]['prob']
                            if random.random() < prob:
                                next_activated.add(v)
                                propagation_path.append((u, v))
                                step_activations.append((u, v, prob))

            if not next_activated:
                break

            step_info.append({
                'step': step + 1,
                'newly_activated': list(next_activated),
                'activations': step_activations
            })

            activated |= next_activated
            newly_activated = next_activated

        return activated, propagation_path, step_info

    def reverse_influence_analysis(self, target_node: int, trials: int = 1000) -> Dict:
        """反向影响路径分析：找出影响目标节点的关键路径"""
        node_influence_count = defaultdict(int)
        path_frequency = defaultdict(int)
        successful_trials = 0
        all_paths_to_target = []

        print(f"🔍 开始反向影响分析，目标：{self.node_to_name.get(target_node, f'节点{target_node}')}")

        for trial in range(trials):
            if trial % 100 == 0:
                print(f"   进度: {trial}/{trials}")

            # 随机选择种子节点（优先选择负面影响源）
            if random.random() < 0.7 and self.negative_sources:
                seeds = [random.choice(list(self.negative_sources.keys()))]
            else:
                seeds = [random.randint(0, self.G.number_of_nodes() - 1)]

            activated, path, step_info = self.simulate_IC_model(seeds)

            if target_node in activated:
                successful_trials += 1

                # 记录影响路径
                target_paths = self._extract_paths_to_target(path, target_node)
                all_paths_to_target.extend(target_paths)

                # 统计节点影响频次
                for u, v in path:
                    node_influence_count[u] += 1
                    path_frequency[(u, v)] += 1

        # 分析结果
        analysis_results = {
            'total_trials': trials,
            'successful_trials': successful_trials,
            'success_rate': successful_trials / trials if trials > 0 else 0,
            'node_influence': sorted(node_influence_count.items(), key=lambda x: x[1], reverse=True),
            'path_frequency': sorted(path_frequency.items(), key=lambda x: x[1], reverse=True),
            'paths_to_target': all_paths_to_target
        }

        return analysis_results

    def _extract_paths_to_target(self, all_paths: List[Tuple[int, int]], target: int) -> List[List[int]]:
        """提取到达目标节点的所有路径"""
        # 构建路径图
        path_graph = defaultdict(list)
        for u, v in all_paths:
            path_graph[u].append(v)

        # 找到所有到达目标的路径
        paths_to_target = []

        def dfs_find_paths(current, target, path, visited):
            if current == target:
                paths_to_target.append(path + [current])
                return

            if current in visited or len(path) > 10:  # 限制路径长度避免无限循环
                return

            visited.add(current)
            for neighbor in path_graph[current]:
                dfs_find_paths(neighbor, target, path + [current], visited.copy())

        # 从所有可能的起点开始搜索，使用列表避免字典迭代问题
        start_nodes = list(path_graph.keys())
        for start_node in start_nodes:
            if len(paths_to_target) < 100:  # 限制路径数量
                dfs_find_paths(start_node, target, [], set())

        return paths_to_target

    def influence_blocking_analysis(self, target_node: int, trials: int = 500) -> Dict:
        """影响阻断分析：找出能够阻止负面影响到达目标的关键节点"""
        print(f"🛡️ 开始影响阻断分析...")

        # 基准测试：不阻断任何节点时的影响传播
        baseline_results = self.reverse_influence_analysis(target_node, trials)
        baseline_success_rate = baseline_results['success_rate']

        blocking_effectiveness = {}

        # 测试阻断每个潜在拯救者的效果
        for blocker_id, blocker_name in self.potential_saviors.items():
            if blocker_id in self.G.nodes():
                print(f"   测试阻断节点: {blocker_name}")

                # 临时移除该节点
                temp_G = self.G.copy()
                temp_G.remove_node(blocker_id)

                # 保存原图并替换
                original_G = self.G
                self.G = temp_G

                # 重新分析
                blocked_results = self.reverse_influence_analysis(target_node, trials)
                blocked_success_rate = blocked_results['success_rate']

                # 计算阻断效果
                reduction = baseline_success_rate - blocked_success_rate
                effectiveness = reduction / baseline_success_rate if baseline_success_rate > 0 else 0

                blocking_effectiveness[blocker_id] = {
                    'name': blocker_name,
                    'baseline_rate': baseline_success_rate,
                    'blocked_rate': blocked_success_rate,
                    'reduction': reduction,
                    'effectiveness': effectiveness
                }

                # 恢复原图
                self.G = original_G

        return {
            'baseline_success_rate': baseline_success_rate,
            'blocking_results': sorted(blocking_effectiveness.items(),
                                     key=lambda x: x[1]['effectiveness'], reverse=True)
        }

    def critical_path_analysis(self, target_node: int) -> Dict:
        """关键路径分析：使用图论方法找出最重要的影响路径"""
        print(f"🛤️ 开始关键路径分析...")

        # 计算各种中心性指标
        centrality_metrics = {
            'betweenness': nx.betweenness_centrality(self.G),
            'closeness': nx.closeness_centrality(self.G),
            'pagerank': nx.pagerank(self.G),
            'eigenvector': nx.eigenvector_centrality(self.G, max_iter=1000)
        }

        # 找出到达目标节点的最短路径
        shortest_paths_to_target = {}
        for source in self.G.nodes():
            if source != target_node:
                try:
                    path = nx.shortest_path(self.G, source, target_node)
                    if len(path) > 1:  # 排除自环
                        shortest_paths_to_target[source] = path
                except nx.NetworkXNoPath:
                    continue

        # 分析负面影响源到目标的路径
        negative_paths = {}
        for neg_id, neg_name in self.negative_sources.items():
            if neg_id in shortest_paths_to_target:
                negative_paths[neg_id] = {
                    'name': neg_name,
                    'path': shortest_paths_to_target[neg_id],
                    'length': len(shortest_paths_to_target[neg_id]) - 1,
                    'betweenness_sum': sum(centrality_metrics['betweenness'][node]
                                         for node in shortest_paths_to_target[neg_id])
                }

        return {
            'centrality_metrics': centrality_metrics,
            'shortest_paths_to_target': shortest_paths_to_target,
            'negative_influence_paths': negative_paths,
            'target_centrality': {
                metric: centrality_metrics[metric][target_node]
                for metric in centrality_metrics.keys()
            }
        }

    def vulnerability_assessment(self, target_node: int) -> Dict:
        """脆弱性评估：评估目标节点的网络位置脆弱性"""
        print(f"⚠️ 开始脆弱性评估...")

        # 计算目标节点的网络特征
        in_degree = self.G.in_degree(target_node)
        out_degree = self.G.out_degree(target_node)

        # 分析邻居节点的类型
        predecessors = list(self.G.predecessors(target_node))
        successors = list(self.G.successors(target_node))

        # 统计不同类型邻居的数量
        negative_predecessors = [p for p in predecessors if p in self.negative_sources]
        savior_predecessors = [p for p in predecessors if p in self.potential_saviors]
        neutral_predecessors = [p for p in predecessors if p not in self.negative_sources and p not in self.potential_saviors]

        # 计算风险评分
        risk_score = 0
        risk_factors = []

        # 负面影响源的直接连接
        if negative_predecessors:
            risk_score += len(negative_predecessors) * 0.3
            risk_factors.append(f"直接受到{len(negative_predecessors)}个负面影响源影响")

        # 缺乏保护者
        if not savior_predecessors:
            risk_score += 0.2
            risk_factors.append("缺乏直接保护者")

        # 入度过高（容易受影响）
        if in_degree > 5:
            risk_score += 0.1
            risk_factors.append(f"入度过高({in_degree})，容易受多方影响")

        # 出度过低（影响力有限）
        if out_degree < 2:
            risk_score += 0.1
            risk_factors.append(f"出度过低({out_degree})，自身影响力有限")

        return {
            'target_node': target_node,
            'target_name': self.node_to_name.get(target_node, f'节点{target_node}'),
            'in_degree': in_degree,
            'out_degree': out_degree,
            'negative_predecessors': [(p, self.node_to_name.get(p, f'节点{p}')) for p in negative_predecessors],
            'savior_predecessors': [(p, self.node_to_name.get(p, f'节点{p}')) for p in savior_predecessors],
            'neutral_predecessors': [(p, self.node_to_name.get(p, f'节点{p}')) for p in neutral_predecessors],
            'risk_score': risk_score,
            'risk_factors': risk_factors,
            'vulnerability_level': self._get_vulnerability_level(risk_score)
        }

    def _get_vulnerability_level(self, risk_score: float) -> str:
        """根据风险评分确定脆弱性等级"""
        if risk_score >= 0.7:
            return "极高风险"
        elif risk_score >= 0.5:
            return "高风险"
        elif risk_score >= 0.3:
            return "中等风险"
        elif risk_score >= 0.1:
            return "低风险"
        else:
            return "安全"

    def visualize_influence_network(self, target_node: int, save_path: str = None):
        """可视化影响网络"""
        print(f"🎨 生成影响网络可视化图...")

        plt.figure(figsize=(15, 12))

        # 创建子图布局
        pos = nx.spring_layout(self.G, k=1, iterations=50)

        # 节点颜色映射
        node_colors = []
        node_sizes = []

        for node in self.G.nodes():
            if node == target_node:
                node_colors.append('red')
                node_sizes.append(800)
            elif node in self.negative_sources:
                node_colors.append('darkred')
                node_sizes.append(600)
            elif node in self.potential_saviors:
                node_colors.append('green')
                node_sizes.append(600)
            elif node in self.neutral_influencers:
                node_colors.append('orange')
                node_sizes.append(400)
            else:
                node_colors.append('lightblue')
                node_sizes.append(200)

        # 绘制网络
        nx.draw_networkx_nodes(self.G, pos, node_color=node_colors,
                              node_size=node_sizes, alpha=0.8)
        nx.draw_networkx_edges(self.G, pos, alpha=0.3, width=0.5,
                              edge_color='gray', arrows=True, arrowsize=10)

        # 添加重要节点标签
        important_nodes = {**self.negative_sources, **self.potential_saviors,
                          target_node: self.node_to_name.get(target_node, f'节点{target_node}')}

        important_pos = {node: pos[node] for node in important_nodes.keys() if node in pos}
        important_labels = {node: name for node, name in important_nodes.items() if node in pos}

        nx.draw_networkx_labels(self.G, important_pos, important_labels,
                               font_size=8, font_family='SimHei')

        plt.title(f'红楼梦人物关系网络：{self.node_to_name.get(target_node)}影响分析',
                 fontsize=16, fontweight='bold')

        # 添加图例
        legend_elements = [
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red',
                      markersize=15, label=f'目标人物({self.node_to_name.get(target_node)})'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='darkred',
                      markersize=12, label='负面影响源'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='green',
                      markersize=12, label='潜在拯救者'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='orange',
                      markersize=10, label='中性影响者'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='lightblue',
                      markersize=8, label='其他人物')
        ]
        plt.legend(handles=legend_elements, loc='upper right')

        plt.axis('off')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 网络图已保存到: {save_path}")

        plt.show()

    def generate_comprehensive_report(self, target_node: int, trials: int = 1000) -> str:
        """生成综合分析报告"""
        print(f"📊 生成综合分析报告...")

        # 执行各种分析
        influence_results = self.reverse_influence_analysis(target_node, trials)
        blocking_results = self.influence_blocking_analysis(target_node, trials//2)
        critical_path_results = self.critical_path_analysis(target_node)
        vulnerability_results = self.vulnerability_assessment(target_node)

        # 生成报告
        result_dir = "daiyu_prediction"
        if not os.path.exists(result_dir):
            os.makedirs(result_dir)

        report_file = os.path.join(result_dir, "黛玉之死影响力分析报告.txt")

        with open(report_file, 'w', encoding='utf-8') as f:
            self._write_report_header(f, target_node, trials)
            self._write_influence_analysis(f, influence_results)
            self._write_blocking_analysis(f, blocking_results)
            self._write_critical_path_analysis(f, critical_path_results)
            self._write_vulnerability_analysis(f, vulnerability_results)
            self._write_conclusions_and_recommendations(f, influence_results, blocking_results, vulnerability_results)

        # 生成可视化
        viz_file = os.path.join(result_dir, "影响网络图.png")
        self.visualize_influence_network(target_node, viz_file)

        return report_file

    def _write_report_header(self, f, target_node: int, trials: int):
        """写入报告头部"""
        f.write("=" * 100 + "\n")
        f.write("红楼梦关系网络分析：基于影响力最大化理论的黛玉之死预测分析\n")
        f.write("=" * 100 + "\n")
        f.write(f"分析时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"目标人物：{self.node_to_name.get(target_node, f'节点{target_node}')} (节点编号: {target_node})\n")
        f.write(f"分析方法：独立级联模型(IC) + 影响阻断分析 + 关键路径分析\n")
        f.write(f"模拟次数：{trials}\n")
        f.write(f"网络规模：{self.G.number_of_nodes()}个节点，{self.G.number_of_edges()}条边\n\n")

    def _write_influence_analysis(self, f, results: Dict):
        """写入影响分析结果"""
        f.write("📈 一、反向影响传播分析\n")
        f.write("-" * 80 + "\n")
        f.write(f"总模拟次数：{results['total_trials']}\n")
        f.write(f"成功激活目标次数：{results['successful_trials']}\n")
        f.write(f"激活成功率：{results['success_rate']:.2%}\n\n")

        f.write("🎯 影响林黛玉的关键人物（按影响频次排序）：\n")
        f.write("-" * 60 + "\n")
        for i, (node, freq) in enumerate(results['node_influence'][:15], 1):
            name = self.node_to_name.get(node, f"未知人物{node}")
            percentage = (freq / results['successful_trials']) * 100 if results['successful_trials'] > 0 else 0
            character_type = self._get_character_type(node)
            f.write(f"{i:2d}. {name:<15} (节点{node:3d}) - 频次: {freq:3d} ({percentage:5.1f}%) [{character_type}]\n")

        f.write("\n🛤️ 导致林黛玉悲剧的关键路径（频次最高的前15条）：\n")
        f.write("-" * 70 + "\n")
        for i, ((u, v), freq) in enumerate(results['path_frequency'][:15], 1):
            u_name = self.node_to_name.get(u, f"未知人物{u}")
            v_name = self.node_to_name.get(v, f"未知人物{v}")
            f.write(f"{i:2d}. {u_name:<15} → {v_name:<15} (频次: {freq})\n")
        f.write("\n")

    def _write_blocking_analysis(self, f, results: Dict):
        """写入阻断分析结果"""
        f.write("🛡️ 二、影响阻断分析（潜在拯救者效果评估）\n")
        f.write("-" * 80 + "\n")
        f.write(f"基准激活成功率：{results['baseline_success_rate']:.2%}\n\n")

        f.write("🦸 潜在拯救者阻断效果排名：\n")
        f.write("-" * 60 + "\n")
        for i, (node_id, data) in enumerate(results['blocking_results'], 1):
            f.write(f"{i}. {data['name']:<15} (节点{node_id:3d})\n")
            f.write(f"   阻断后成功率：{data['blocked_rate']:.2%}\n")
            f.write(f"   减少幅度：{data['reduction']:.2%}\n")
            f.write(f"   阻断效果：{data['effectiveness']:.1%}\n\n")

    def _write_critical_path_analysis(self, f, results: Dict):
        """写入关键路径分析结果"""
        f.write("🔍 三、关键路径分析（基于图论方法）\n")
        f.write("-" * 80 + "\n")

        f.write("📊 目标节点中心性指标：\n")
        for metric, value in results['target_centrality'].items():
            f.write(f"   {metric.capitalize()}: {value:.4f}\n")
        f.write("\n")

        f.write("⚡ 负面影响源到目标的最短路径：\n")
        f.write("-" * 60 + "\n")
        for node_id, data in results['negative_influence_paths'].items():
            f.write(f"{data['name']} → 林黛玉：\n")
            path_names = [self.node_to_name.get(n, f'节点{n}') for n in data['path']]
            f.write(f"   路径：{' → '.join(path_names)}\n")
            f.write(f"   长度：{data['length']}步\n")
            f.write(f"   路径重要性：{data['betweenness_sum']:.4f}\n\n")

    def _write_vulnerability_analysis(self, f, results: Dict):
        """写入脆弱性分析结果"""
        f.write("⚠️ 四、脆弱性评估\n")
        f.write("-" * 80 + "\n")
        f.write(f"目标人物：{results['target_name']}\n")
        f.write(f"网络位置特征：\n")
        f.write(f"   入度：{results['in_degree']} (受影响的连接数)\n")
        f.write(f"   出度：{results['out_degree']} (影响他人的连接数)\n\n")

        f.write(f"🔴 直接负面影响源 ({len(results['negative_predecessors'])}个)：\n")
        for node_id, name in results['negative_predecessors']:
            f.write(f"   - {name} (节点{node_id})\n")

        f.write(f"\n🟢 直接保护者 ({len(results['savior_predecessors'])}个)：\n")
        for node_id, name in results['savior_predecessors']:
            f.write(f"   - {name} (节点{node_id})\n")

        f.write(f"\n🟡 中性影响者 ({len(results['neutral_predecessors'])}个)：\n")
        for node_id, name in results['neutral_predecessors'][:10]:  # 只显示前10个
            f.write(f"   - {name} (节点{node_id})\n")

        f.write(f"\n📈 风险评估：\n")
        f.write(f"   风险评分：{results['risk_score']:.2f}\n")
        f.write(f"   脆弱性等级：{results['vulnerability_level']}\n")
        f.write(f"   主要风险因素：\n")
        for factor in results['risk_factors']:
            f.write(f"   - {factor}\n")
        f.write("\n")

    def _write_conclusions_and_recommendations(self, f, influence_results: Dict,
                                             blocking_results: Dict, vulnerability_results: Dict):
        """写入结论和建议"""
        f.write("💡 五、分析结论与文学解读\n")
        f.write("-" * 80 + "\n")

        f.write("🔮 预测结论：\n")
        success_rate = influence_results['success_rate']
        if success_rate > 0.7:
            f.write(f"   林黛玉之死的概率极高({success_rate:.1%})，悲剧几乎不可避免\n")
        elif success_rate > 0.5:
            f.write(f"   林黛玉面临高度风险({success_rate:.1%})，需要强力干预\n")
        elif success_rate > 0.3:
            f.write(f"   林黛玉处于中等风险({success_rate:.1%})，仍有拯救可能\n")
        else:
            f.write(f"   林黛玉相对安全({success_rate:.1%})，悲剧可以避免\n")

        f.write(f"\n📚 文学分析：\n")

        # 分析主要威胁
        top_threats = influence_results['node_influence'][:3]
        f.write(f"   主要威胁来源：\n")
        for node, freq in top_threats:
            name = self.node_to_name.get(node, f"节点{node}")
            char_type = self._get_character_type(node)
            f.write(f"   - {name}：{char_type}，影响频次{freq}\n")

        # 分析最佳拯救者
        if blocking_results['blocking_results']:
            best_savior = blocking_results['blocking_results'][0]
            f.write(f"\n   最有效的拯救者：{best_savior[1]['name']}\n")
            f.write(f"   - 可将悲剧概率从{best_savior[1]['baseline_rate']:.1%}降至{best_savior[1]['blocked_rate']:.1%}\n")
            f.write(f"   - 阻断效果：{best_savior[1]['effectiveness']:.1%}\n")

        f.write(f"\n🎭 红楼梦情节对应：\n")
        f.write(f"   本分析揭示了林黛玉在贾府中的脆弱地位：\n")
        f.write(f"   - 受到来自王夫人、薛家等多方面的负面影响\n")
        f.write(f"   - 虽有贾宝玉、贾母等人的保护，但力量有限\n")
        f.write(f"   - 网络位置决定了她难以逃脱悲剧命运\n")
        f.write(f"   - 这与原著中黛玉最终的悲剧结局高度吻合\n\n")

        f.write("=" * 100 + "\n")
        f.write("分析完成 - 基于影响力最大化理论的红楼梦人物关系网络分析\n")
        f.write("=" * 100 + "\n")

    def _get_character_type(self, node_id: int) -> str:
        """获取人物类型标签"""
        if node_id in self.negative_sources:
            return "负面影响源"
        elif node_id in self.potential_saviors:
            return "潜在拯救者"
        elif node_id in self.neutral_influencers:
            return "中性影响者"
        else:
            return "普通人物"

def main():
    """主程序：基于影响力最大化理论分析黛玉之死"""
    print("🎭 红楼梦关系网络分析：基于影响力最大化理论的黛玉之死预测")
    print("=" * 80)

    # 文件路径
    network_file = 'network_data.txt'
    mapping_file = 'node_mapping.txt'

    # 创建分析器实例
    analyzer = InfluenceMaximizationAnalyzer()

    # 读取数据
    print("📖 正在读取人物姓名映射...")
    analyzer.node_to_name, analyzer.name_to_node = analyzer.read_node_mapping(mapping_file)
    print(f"✅ 成功读取 {len(analyzer.node_to_name)} 个人物的映射信息")

    print("🌐 正在构建关系网络...")
    analyzer.G = analyzer.read_network(network_file)
    print(f"✅ 网络构建完成，共有 {analyzer.G.number_of_nodes()} 个节点，{analyzer.G.number_of_edges()} 条边")

    # 确认目标人物
    target_node = analyzer.daiyu_id
    target_name = analyzer.node_to_name.get(target_node, f"节点{target_node}")
    print(f"🎯 目标人物：{target_name} (节点编号: {target_node})")

    # 显示关键人物分类
    print(f"\n👥 关键人物分类：")
    print(f"   负面影响源：{', '.join(analyzer.negative_sources.values())}")
    print(f"   潜在拯救者：{', '.join(analyzer.potential_saviors.values())}")
    print(f"   中性影响者：{', '.join(analyzer.neutral_influencers.values())}")

    # 生成综合分析报告
    print(f"\n🔬 开始综合分析...")
    trials = 1000
    report_file = analyzer.generate_comprehensive_report(target_node, trials)

    print(f"\n✅ 综合分析完成！")
    print(f"📄 详细报告已保存到: {report_file}")
    print(f"🖼️ 网络可视化图已生成")

    print(f"\n🎭 分析总结：")
    print(f"   本程序基于影响力最大化理论，通过以下方法分析了林黛玉的悲剧命运：")
    print(f"   1. 独立级联模型(IC)模拟负面影响传播")
    print(f"   2. 反向路径追踪找出关键影响者")
    print(f"   3. 影响阻断分析识别潜在拯救者")
    print(f"   4. 关键路径分析揭示传播机制")
    print(f"   5. 脆弱性评估量化风险等级")
    print(f"\n   这种方法将文学分析与网络科学相结合，")
    print(f"   为理解红楼梦中人物命运提供了新的视角。")

if __name__ == '__main__':
    main()
